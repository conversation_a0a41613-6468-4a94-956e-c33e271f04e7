import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { Star, Heart, ShoppingCart, CheckCircle, XCircle, ExternalLink } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const ProductDetail: React.FC = () => {
  const { id } = useParams();
  console.log('Product ID:', id); // 使用id变量
  const [selectedImage, setSelectedImage] = useState(0);
  
  // Mock data - in real app this would come from API
  const product = {
    id: '1',
    name: 'iPhone 15 Pro Max 256GB',
    price: 1199,
    originalPrice: 1299,
    rating: 4.8,
    reviews: 2847,
    images: [
      '/api/placeholder/600/600',
      '/api/placeholder/600/600',
      '/api/placeholder/600/600',
      '/api/placeholder/600/600'
    ],
    brand: 'Apple',
    category: 'Electronics',
    inStock: true,
    description: 'The iPhone 15 Pro Max features a titanium design, A17 Pro chip, and advanced camera system with 5x telephoto zoom.',
    features: [
      'A17 Pro chip with 6-core GPU',
      '48MP Main camera with 2x Telephoto',
      '5x Telephoto camera',
      'Titanium design',
      'Action Button',
      'USB-C connector',
      'Up to 29 hours video playback'
    ],
    specifications: {
      'Display': '6.7-inch Super Retina XDR',
      'Chip': 'A17 Pro',
      'Storage': '256GB',
      'Camera': '48MP + 12MP + 12MP',
      'Battery': 'Up to 29 hours video',
      'Weight': '221 grams',
      'Colors': 'Natural Titanium, Blue Titanium, White Titanium, Black Titanium'
    },
    platforms: [
      { name: 'Amazon', price: 1199, inStock: true, rating: 4.8 },
      { name: 'Apple Store', price: 1199, inStock: true, rating: 4.9 },
      { name: 'Best Buy', price: 1249, inStock: false, rating: 4.7 },
      { name: 'Target', price: 1199, inStock: true, rating: 4.6 }
    ]
  };

  // Mock price history data
  const priceHistory = [
    { date: '2024-01', price: 1299 },
    { date: '2024-02', price: 1279 },
    { date: '2024-03', price: 1249 },
    { date: '2024-04', price: 1229 },
    { date: '2024-05', price: 1199 },
    { date: '2024-06', price: 1199 }
  ];

  const aiSummary = {
    pros: [
      'Excellent camera system with 5x telephoto zoom',
      'Premium titanium build quality',
      'Outstanding performance with A17 Pro chip',
      'Long battery life',
      'Great display quality'
    ],
    cons: [
      'Very expensive compared to alternatives',
      'No significant design changes from previous model',
      'Limited storage options',
      'Heavy weight'
    ],
    recommendation: 'Highly recommended for users who prioritize camera quality and premium build. Consider alternatives if budget is a concern.'
  };

  return (
    <div className="min-h-screen bg-white py-8">
      <div className="max-w-7xl mx-auto px-4">
        {/* Breadcrumb */}
        <nav className="mb-8 text-sm text-gray-600">
          <span>Home</span> / <span>Electronics</span> / <span>Smartphones</span> / <span className="text-gray-dark">{product.name}</span>
        </nav>

        <div className="grid lg:grid-cols-2 gap-12 mb-12">
          {/* Product Images */}
          <div className="space-y-4">
            <div className="aspect-square bg-gray-100 rounded-2xl overflow-hidden">
              <img
                src={product.images[selectedImage]}
                alt={product.name}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="grid grid-cols-4 gap-4">
              {product.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`aspect-square bg-gray-100 rounded-lg overflow-hidden border-2 transition-colors duration-200 ${
                    selectedImage === index ? 'border-primary-500' : 'border-transparent hover:border-gray-300'
                  }`}
                >
                  <img src={image} alt={`${product.name} ${index + 1}`} className="w-full h-full object-cover" />
                </button>
              ))}
            </div>
          </div>

          {/* Product Info */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-dark mb-2">{product.name}</h1>
              <p className="text-lg text-gray-600">{product.brand}</p>
            </div>

            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <div className="flex items-center mr-2">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`w-5 h-5 ${
                        i < Math.floor(product.rating) ? 'text-accent-yellow fill-current' : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
                <span className="font-medium">{product.rating}</span>
              </div>
              <span className="text-gray-600">({product.reviews} reviews)</span>
            </div>

            <div className="flex items-center space-x-4">
              <span className="text-4xl font-bold text-gray-dark">${product.price}</span>
              {product.originalPrice && (
                <span className="text-2xl text-gray-500 line-through">${product.originalPrice}</span>
              )}
              {product.originalPrice && (
                <span className="bg-red-100 text-red-600 px-3 py-1 rounded-full text-sm font-medium">
                  Save ${product.originalPrice - product.price}
                </span>
              )}
            </div>

            <div className={`flex items-center space-x-2 ${product.inStock ? 'text-green-600' : 'text-red-600'}`}>
              {product.inStock ? <CheckCircle className="w-5 h-5" /> : <XCircle className="w-5 h-5" />}
              <span className="font-medium">{product.inStock ? 'In Stock' : 'Out of Stock'}</span>
            </div>

            <p className="text-gray-600 leading-relaxed">{product.description}</p>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-dark">Key Features</h3>
              <ul className="grid grid-cols-1 gap-2">
                {product.features.map((feature, index) => (
                  <li key={index} className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                    <span className="text-gray-600">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div className="flex space-x-4">
              <button className="btn-primary flex-1">
                <ShoppingCart className="w-5 h-5 mr-2" />
                Add to Cart
              </button>
              <button className="btn-secondary px-6">
                <Heart className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>

        {/* Platform Comparison */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-dark mb-6">Compare Prices Across Platforms</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
            {product.platforms.map((platform, index) => (
              <div key={index} className="card">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold text-gray-dark">{platform.name}</h3>
                  <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                    platform.inStock ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'
                  }`}>
                    {platform.inStock ? 'In Stock' : 'Out of Stock'}
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="text-2xl font-bold text-gray-dark">${platform.price}</div>
                  <div className="flex items-center">
                    <Star className="w-4 h-4 text-accent-yellow fill-current mr-1" />
                    <span className="text-sm">{platform.rating}</span>
                  </div>
                  <button className="w-full btn-primary text-sm" disabled={!platform.inStock}>
                    <ExternalLink className="w-4 h-4 mr-2" />
                    View on {platform.name}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Price History Chart */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-dark mb-6">Price History</h2>
          <div className="card">
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={priceHistory}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Line type="monotone" dataKey="price" stroke="#3B82F6" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>

        {/* AI Summary */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-dark mb-6">AI Analysis</h2>
          <div className="grid md:grid-cols-2 gap-8">
            <div className="card">
              <h3 className="text-lg font-semibold text-green-600 mb-4 flex items-center">
                <CheckCircle className="w-5 h-5 mr-2" />
                Pros
              </h3>
              <ul className="space-y-2">
                {aiSummary.pros.map((pro, index) => (
                  <li key={index} className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-gray-600">{pro}</span>
                  </li>
                ))}
              </ul>
            </div>
            
            <div className="card">
              <h3 className="text-lg font-semibold text-red-600 mb-4 flex items-center">
                <XCircle className="w-5 h-5 mr-2" />
                Cons
              </h3>
              <ul className="space-y-2">
                {aiSummary.cons.map((con, index) => (
                  <li key={index} className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-gray-600">{con}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
          
          <div className="card mt-6">
            <h3 className="text-lg font-semibold text-primary-600 mb-3">AI Recommendation</h3>
            <p className="text-gray-600 leading-relaxed">{aiSummary.recommendation}</p>
          </div>
        </div>

        {/* Specifications */}
        <div>
          <h2 className="text-2xl font-bold text-gray-dark mb-6">Specifications</h2>
          <div className="card">
            <div className="grid md:grid-cols-2 gap-4">
              {Object.entries(product.specifications).map(([key, value]) => (
                <div key={key} className="flex justify-between py-3 border-b border-gray-100 last:border-b-0">
                  <span className="font-medium text-gray-dark">{key}</span>
                  <span className="text-gray-600">{value}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetail;
