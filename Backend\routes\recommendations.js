const express = require('express');
const db = require('../config/db');
const router = express.Router();

// Helper function to simulate AI scoring
const addMockAIScores = (products) => {
  return products.map(p => ({
    ...p,
    aiScore: Math.floor(p.rating * 15 + Math.random() * 30), // Simple score based on rating
    reasons: [
      'Good match for your preferences',
      p.rating > 4.5 ? 'Highly rated by users' : 'Popular choice',
      p.in_stock ? 'Available for immediate dispatch' : 'Currently out of stock'
    ]
  }));
};

// GET /api/recommendations - Get AI-powered product recommendations
router.get('/', async (req, res) => {
  try {
    const { category, budget, limit = 10 } = req.query;
    await new Promise(resolve => setTimeout(resolve, 400)); // Simulate AI delay

    let query = 'SELECT * FROM products WHERE in_stock = true';
    let queryParams = [];

    if (category) {
      queryParams.push(category);
      query += ` AND category = $${queryParams.length}`;
    }
    if (budget) {
      queryParams.push(budget);
      query += ` AND price <= $${queryParams.length}`;
    }

    query += ' ORDER BY rating DESC, reviews_count DESC';
    queryParams.push(limit);
    query += ` LIMIT $${queryParams.length}`;

    const productsResult = await db.query(query, queryParams);
    let recommendations = addMockAIScores(productsResult.rows);

    res.json({
      success: true,
      data: {
        recommendations,
        personalization: { /* Mock data */ },
        totalCount: recommendations.length,
      },
    });
  } catch (error) {
    console.error('Failed to fetch recommendations:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// POST /api/recommendations/feedback - Submit recommendation feedback
router.post('/feedback', async (req, res) => {
  try {
    const { productId, userId, rating, feedback, helpful } = req.body;
    if (!productId || !rating) {
      return res.status(400).json({ success: false, error: 'Product ID and rating are required' });
    }

    const insertQuery = `
      INSERT INTO recommendation_feedback (product_id, user_id, rating, feedback, helpful)
      VALUES ($1, $2, $3, $4, $5) RETURNING id;
    `;
    const result = await db.query(insertQuery, [productId, userId, rating, feedback, helpful]);

    res.status(201).json({
      success: true,
      data: {
        message: 'Feedback received successfully',
        feedbackId: result.rows[0].id,
      },
    });
  } catch (error) {
    console.error('Failed to submit feedback:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

module.exports = router;