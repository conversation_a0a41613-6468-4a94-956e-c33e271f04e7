const express = require('express');
const router = express.Router();

// Mock recommendation data
const mockRecommendations = [
  {
    id: '1',
    name: 'iPhone 15 Pro Max 256GB',
    price: 1199,
    originalPrice: 1299,
    rating: 4.8,
    reviews: 2847,
    image: 'https://via.placeholder.com/300x300/3B82F6/FFFFFF?text=iPhone+15+Pro',
    brand: 'Apple',
    category: 'Electronics',
    platform: 'Amazon',
    inStock: true,
    features: ['A17 Pro Chip', '48MP Camera', 'Titanium Design'],
    aiScore: 95,
    reasons: [
      'Matches your preference for premium smartphones',
      'Excellent camera quality based on your photo needs',
      'Strong performance for productivity apps'
    ]
  },
  {
    id: '2',
    name: 'Samsung Galaxy S24 Ultra',
    price: 1099,
    rating: 4.7,
    reviews: 1923,
    image: 'https://via.placeholder.com/300x300/6366F1/FFFFFF?text=Galaxy+S24',
    brand: 'Samsung',
    category: 'Electronics',
    platform: 'Best Buy',
    inStock: true,
    features: ['S Pen Included', '200MP Camera', '5000mAh Battery'],
    aiScore: 88,
    reasons: [
      'Great value for money in premium segment',
      'S Pen functionality for productivity',
      'Superior battery life'
    ]
  },
  {
    id: '3',
    name: 'MacBook Air M3 13-inch',
    price: 1099,
    originalPrice: 1199,
    rating: 4.9,
    reviews: 3421,
    image: 'https://via.placeholder.com/300x300/10B981/FFFFFF?text=MacBook+Air',
    brand: 'Apple',
    category: 'Electronics',
    platform: 'Apple Store',
    inStock: false,
    features: ['M3 Chip', '18-hour Battery', 'Liquid Retina Display'],
    aiScore: 92,
    reasons: [
      'Perfect for your work-from-home setup',
      'Excellent battery life for all-day use',
      'Lightweight and portable design'
    ]
  }
];

// GET /api/recommendations - Get AI-powered product recommendations
router.get('/', async (req, res) => {
  try {
    const {
      category,
      budget,
      preferences,
      userId,
      limit = 10
    } = req.query;

    // Simulate AI processing delay
    await new Promise(resolve => setTimeout(resolve, 400));

    let recommendations = [...mockRecommendations];

    // Apply category filter
    if (category) {
      recommendations = recommendations.filter(p => 
        p.category.toLowerCase() === category.toLowerCase()
      );
    }

    // Apply budget filter
    if (budget) {
      const budgetNum = parseFloat(budget);
      recommendations = recommendations.filter(p => p.price <= budgetNum);
    }

    // Sort by AI score
    recommendations.sort((a, b) => b.aiScore - a.aiScore);

    // Limit results
    recommendations = recommendations.slice(0, parseInt(limit));

    // Add personalization metadata
    const personalization = {
      basedOn: [
        'Your browsing history',
        'Similar user preferences',
        'Current market trends',
        'Price optimization'
      ],
      confidence: 0.87,
      lastUpdated: new Date().toISOString()
    };

    res.json({
      success: true,
      data: {
        recommendations,
        personalization,
        totalCount: recommendations.length,
        processingTime: '0.4s'
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to fetch recommendations',
      message: error.message
    });
  }
});

// POST /api/recommendations/personalized - Get personalized recommendations
router.post('/personalized', async (req, res) => {
  try {
    const {
      userPreferences = {},
      searchHistory = [],
      budget,
      category,
      urgency = 'normal'
    } = req.body;

    // Simulate AI processing
    await new Promise(resolve => setTimeout(resolve, 600));

    // Mock personalized recommendations based on input
    let personalizedRecs = mockRecommendations.map(product => ({
      ...product,
      personalizedScore: Math.random() * 100,
      personalizedReasons: [
        `Matches your ${userPreferences.style || 'preferred'} style`,
        `Within your budget of $${budget || '1500'}`,
        `Based on your interest in ${category || 'electronics'}`
      ]
    }));

    // Sort by personalized score
    personalizedRecs.sort((a, b) => b.personalizedScore - a.personalizedScore);

    const aiInsights = {
      userProfile: {
        preferredBrands: userPreferences.brands || ['Apple', 'Samsung'],
        budgetRange: budget ? [budget * 0.8, budget * 1.2] : [500, 2000],
        categories: [category || 'Electronics'],
        urgency: urgency
      },
      recommendations: {
        totalAnalyzed: 10000,
        personalizedResults: personalizedRecs.length,
        confidenceScore: 0.91,
        factors: [
          'Price optimization',
          'Feature matching',
          'Brand preference',
          'User behavior patterns'
        ]
      }
    };

    res.json({
      success: true,
      data: {
        recommendations: personalizedRecs.slice(0, 8),
        aiInsights,
        processingTime: '0.6s'
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to generate personalized recommendations',
      message: error.message
    });
  }
});

// GET /api/recommendations/similar/:productId - Get similar products
router.get('/similar/:productId', async (req, res) => {
  try {
    const { productId } = req.params;
    const { limit = 6 } = req.query;

    // Find the base product
    const baseProduct = mockRecommendations.find(p => p.id === productId);
    
    if (!baseProduct) {
      return res.status(404).json({
        success: false,
        error: 'Product not found'
      });
    }

    // Simulate AI processing
    await new Promise(resolve => setTimeout(resolve, 300));

    // Mock similar products (excluding the base product)
    const similarProducts = mockRecommendations
      .filter(p => p.id !== productId)
      .map(product => ({
        ...product,
        similarityScore: Math.random() * 100,
        similarityReasons: [
          `Same category: ${baseProduct.category}`,
          `Similar price range`,
          `Comparable features and quality`
        ]
      }))
      .sort((a, b) => b.similarityScore - a.similarityScore)
      .slice(0, parseInt(limit));

    res.json({
      success: true,
      data: {
        baseProduct: {
          id: baseProduct.id,
          name: baseProduct.name,
          category: baseProduct.category
        },
        similarProducts,
        totalFound: similarProducts.length,
        processingTime: '0.3s'
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to fetch similar products',
      message: error.message
    });
  }
});

// POST /api/recommendations/feedback - Submit recommendation feedback
router.post('/feedback', async (req, res) => {
  try {
    const {
      productId,
      userId,
      rating,
      feedback,
      helpful = true
    } = req.body;

    if (!productId || !rating) {
      return res.status(400).json({
        success: false,
        error: 'Product ID and rating are required'
      });
    }

    // In a real app, this would save to database
    console.log('Recommendation feedback received:', {
      productId,
      userId,
      rating,
      feedback,
      helpful,
      timestamp: new Date().toISOString()
    });

    res.json({
      success: true,
      data: {
        message: 'Feedback received successfully',
        feedbackId: `feedback_${Date.now()}`,
        aiLearning: 'Your feedback helps improve our recommendations'
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to submit feedback',
      message: error.message
    });
  }
});

// GET /api/recommendations/trending - Get trending recommendations
router.get('/trending', async (req, res) => {
  try {
    const { category, timeframe = '7d' } = req.query;

    // Simulate processing
    await new Promise(resolve => setTimeout(resolve, 200));

    let trendingProducts = mockRecommendations.map(product => ({
      ...product,
      trendScore: Math.random() * 100,
      trendingReasons: [
        'High user engagement this week',
        'Increasing search volume',
        'Positive review momentum'
      ]
    }));

    if (category) {
      trendingProducts = trendingProducts.filter(p => 
        p.category.toLowerCase() === category.toLowerCase()
      );
    }

    trendingProducts.sort((a, b) => b.trendScore - a.trendScore);

    res.json({
      success: true,
      data: {
        trending: trendingProducts.slice(0, 8),
        timeframe,
        category: category || 'all',
        lastUpdated: new Date().toISOString()
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to fetch trending recommendations',
      message: error.message
    });
  }
});

module.exports = router;
