import React, { useState, useEffect } from 'react';
import { Filter, Grid, List, Star, Heart, ShoppingCart, ArrowUpDown } from 'lucide-react';
import { productApi, type Product } from '../services/api';



const Recommendations: React.FC = () => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [sortBy, setSortBy] = useState('relevance');
  const [selectedCategory] = useState('all');
  const [priceRange] = useState([0, 2000]);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch products from API
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        const response = await productApi.getProducts({
          category: selectedCategory === 'all' ? undefined : selectedCategory,
          minPrice: priceRange[0],
          maxPrice: priceRange[1],
          sortBy: sortBy === 'relevance' ? undefined : sortBy,
          limit: 12
        });

        if (response.success && response.data) {
          setProducts(response.data.products);
        } else {
          setError(response.error || 'Failed to fetch products');
        }
      } catch (err) {
        setError('Failed to fetch products');
        console.error('Error fetching products:', err);
        // 使用mockProducts作为fallback
        setProducts(mockProducts);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [selectedCategory, priceRange, sortBy]);

  // Mock data fallback for development
  const mockProducts: Product[] = [
    {
      id: '1',
      name: 'iPhone 15 Pro Max 256GB',
      price: 1199,
      originalPrice: 1299,
      rating: 4.8,
      reviews: 2847,
      image: '/api/placeholder/300/300',
      brand: 'Apple',
      category: 'Electronics',
      platform: 'Amazon',
      inStock: true,
      features: ['A17 Pro Chip', '48MP Camera', 'Titanium Design']
    },
    {
      id: '2',
      name: 'Samsung Galaxy S24 Ultra',
      price: 1099,
      rating: 4.7,
      reviews: 1923,
      image: '/api/placeholder/300/300',
      brand: 'Samsung',
      category: 'Electronics',
      platform: 'Best Buy',
      inStock: true,
      features: ['S Pen Included', '200MP Camera', '5000mAh Battery']
    },
    {
      id: '3',
      name: 'MacBook Air M3 13-inch',
      price: 1099,
      originalPrice: 1199,
      rating: 4.9,
      reviews: 3421,
      image: '/api/placeholder/300/300',
      brand: 'Apple',
      category: 'Electronics',
      platform: 'Apple Store',
      inStock: false,
      features: ['M3 Chip', '18-hour Battery', 'Liquid Retina Display']
    }
  ];

  const filters = {
    priceRange: [0, 2000],
    brands: ['Apple', 'Samsung', 'Google', 'Sony'],
    categories: ['Electronics', 'Fashion', 'Home'],
    rating: [4, 5],
    platforms: ['Amazon', 'Best Buy', 'Apple Store', 'Target']
  };

  const ProductCard: React.FC<{ product: Product }> = ({ product }) => (
    <div className="card group hover:shadow-soft-lg transition-all duration-300 transform hover:-translate-y-1">
      <div className="relative mb-4">
        <img
          src={product.image}
          alt={product.name}
          className="w-full h-48 object-cover rounded-lg bg-gray-100"
        />
        <button className="absolute top-3 right-3 p-2 bg-white rounded-full shadow-soft opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <Heart className="w-4 h-4 text-gray-600 hover:text-red-500" />
        </button>
        {product.originalPrice && (
          <div className="absolute top-3 left-3 bg-red-500 text-white px-2 py-1 rounded-md text-xs font-medium">
            Save ${product.originalPrice - product.price}
          </div>
        )}
        {!product.inStock && (
          <div className="absolute inset-0 bg-black/50 rounded-lg flex items-center justify-center">
            <span className="text-white font-medium">Out of Stock</span>
          </div>
        )}
      </div>
      
      <div className="space-y-3">
        <div>
          <h3 className="font-semibold text-gray-dark line-clamp-2 mb-1">{product.name}</h3>
          <p className="text-sm text-gray-500">{product.brand} • {product.platform}</p>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className="flex items-center">
            <Star className="w-4 h-4 text-accent-yellow fill-current" />
            <span className="text-sm font-medium ml-1">{product.rating}</span>
          </div>
          <span className="text-sm text-gray-500">({product.reviews} reviews)</span>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className="text-2xl font-bold text-gray-dark">${product.price}</span>
          {product.originalPrice && (
            <span className="text-lg text-gray-500 line-through">${product.originalPrice}</span>
          )}
        </div>
        
        <div className="flex flex-wrap gap-1">
          {product.features.slice(0, 2).map((feature, index) => (
            <span key={index} className="text-xs bg-primary-50 text-primary-600 px-2 py-1 rounded-md">
              {feature}
            </span>
          ))}
        </div>
        
        <div className="flex space-x-2 pt-2">
          <button className="btn-primary flex-1 text-sm">
            <ShoppingCart className="w-4 h-4 mr-2" />
            Compare
          </button>
          <button className="btn-secondary px-4 text-sm">
            View Details
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-dark mb-2">AI Recommendations</h1>
          <p className="text-gray-600">Showing {products.length} products based on your preferences</p>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Filters Sidebar */}
          <div className={`lg:w-80 ${showFilters ? 'block' : 'hidden lg:block'}`}>
            <div className="card sticky top-24">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-lg font-semibold text-gray-dark">Filters</h2>
                <button className="text-primary-500 text-sm font-medium">Clear All</button>
              </div>
              
              {/* Price Range */}
              <div className="mb-6">
                <h3 className="font-medium text-gray-dark mb-3">Price Range</h3>
                <div className="space-y-2">
                  <input
                    type="range"
                    min="0"
                    max="2000"
                    className="w-full accent-primary-500"
                  />
                  <div className="flex justify-between text-sm text-gray-600">
                    <span>$0</span>
                    <span>$2000+</span>
                  </div>
                </div>
              </div>
              
              {/* Brands */}
              <div className="mb-6">
                <h3 className="font-medium text-gray-dark mb-3">Brands</h3>
                <div className="space-y-2">
                  {filters.brands.map((brand) => (
                    <label key={brand} className="flex items-center">
                      <input type="checkbox" className="rounded border-gray-300 text-primary-500 focus:ring-primary-500" />
                      <span className="ml-2 text-sm text-gray-600">{brand}</span>
                    </label>
                  ))}
                </div>
              </div>
              
              {/* Rating */}
              <div className="mb-6">
                <h3 className="font-medium text-gray-dark mb-3">Minimum Rating</h3>
                <div className="space-y-2">
                  {[4, 3, 2, 1].map((rating) => (
                    <label key={rating} className="flex items-center">
                      <input type="radio" name="rating" className="text-primary-500 focus:ring-primary-500" />
                      <div className="ml-2 flex items-center">
                        {[...Array(rating)].map((_, i) => (
                          <Star key={i} className="w-4 h-4 text-accent-yellow fill-current" />
                        ))}
                        <span className="ml-1 text-sm text-gray-600">& up</span>
                      </div>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {/* Controls */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="lg:hidden btn-secondary"
                >
                  <Filter className="w-4 h-4 mr-2" />
                  Filters
                </button>
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 rounded-lg transition-colors duration-200 ${
                      viewMode === 'grid' ? 'bg-primary-500 text-white' : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    <Grid className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 rounded-lg transition-colors duration-200 ${
                      viewMode === 'list' ? 'bg-primary-500 text-white' : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    <List className="w-4 h-4" />
                  </button>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <ArrowUpDown className="w-4 h-4 text-gray-600" />
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="border border-gray-200 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent outline-none"
                >
                  <option value="relevance">Most Relevant</option>
                  <option value="price-low">Price: Low to High</option>
                  <option value="price-high">Price: High to Low</option>
                  <option value="rating">Highest Rated</option>
                  <option value="reviews">Most Reviews</option>
                </select>
              </div>
            </div>

            {/* Loading State */}
            {loading && (
              <div className="flex justify-center items-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
              </div>
            )}

            {/* Error State */}
            {error && (
              <div className="text-center py-12">
                <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
                  <p className="text-red-600 mb-4">{error}</p>
                  <button
                    onClick={() => window.location.reload()}
                    className="btn-primary"
                  >
                    Try Again
                  </button>
                </div>
              </div>
            )}

            {/* Products Grid */}
            {!loading && !error && (
              <div className={`grid gap-6 ${
                viewMode === 'grid'
                  ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'
                  : 'grid-cols-1'
              }`}>
                {products.length > 0 ? (
                  products.map((product) => (
                    <ProductCard key={product.id} product={product} />
                  ))
                ) : (
                  <div className="col-span-full text-center py-12">
                    <p className="text-gray-600 text-lg">No products found matching your criteria.</p>
                  </div>
                )}
              </div>
            )}

            {/* Load More */}
            <div className="text-center mt-12">
              <button className="btn-secondary px-8 py-3">
                Load More Products
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Recommendations;
