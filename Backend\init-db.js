const db = require('./config/db');

const createTables = async () => {
  const createUsersTableQuery = `
    CREATE TABLE IF NOT EXISTS users (
      id SERIAL PRIMARY KEY,
      email VARCHAR(255) UNIQUE NOT NULL,
      password VARCHAR(255) NOT NULL,
      name VARCHAR(255) NOT NULL,
      preferences JSONB,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );
  `;

  const createProductsTableQuery = `
    CREATE TABLE IF NOT EXISTS products (
      id SERIAL PRIMARY KEY,
      name VARCHAR(255) NOT NULL,
      price DECIMAL(10, 2) NOT NULL,
      original_price DECIMAL(10, 2),
      rating DECIMAL(2, 1),
      reviews_count INTEGER,
      image_url TEXT,
      brand VARCHAR(100),
      category VARCHAR(100),
      platform VARCHAR(100),
      in_stock BOOLEAN DEFAULT true,
      features J<PERSON><PERSON><PERSON>,
      description TEXT,
      specifications JSONB,
      platforms_data JSONB,
      price_history JSONB,
      ai_analysis JSONB,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );
  `;

  try {
    await db.query(createUsersTableQuery);
    console.log('Table "users" is ready.');
    await db.query(createProductsTableQuery);
    console.log('Table "products" is ready.');

    const createFeedbackTableQuery = `
      CREATE TABLE IF NOT EXISTS recommendation_feedback (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
        product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
        rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
        feedback TEXT,
        helpful BOOLEAN,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
    `;
    await db.query(createFeedbackTableQuery);
    console.log('Table "recommendation_feedback" is ready.');
  } catch (err) {
    console.error('Error creating tables:', err.stack);
  }
};

const insertMockData = async () => {
  const mockProducts = [
  {
    id: '1',
    name: 'iPhone 15 Pro Max 256GB',
    price: 1199,
    originalPrice: 1299,
    rating: 4.8,
    reviews: 2847,
    image: 'https://via.placeholder.com/300x300/3B82F6/FFFFFF?text=iPhone+15+Pro',
    brand: 'Apple',
    category: 'Electronics',
    platform: 'Amazon',
    inStock: true,
    features: ['A17 Pro Chip', '48MP Camera', 'Titanium Design'],
    description: 'The iPhone 15 Pro Max features a titanium design, A17 Pro chip, and advanced camera system.',
    specifications: {
      'Display': '6.7-inch Super Retina XDR',
      'Chip': 'A17 Pro',
      'Storage': '256GB',
      'Camera': '48MP + 12MP + 12MP',
      'Battery': 'Up to 29 hours video',
      'Weight': '221 grams'
    },
    platforms: [
      { name: 'Amazon', price: 1199, inStock: true, rating: 4.8 },
      { name: 'Apple Store', price: 1199, inStock: true, rating: 4.9 },
      { name: 'Best Buy', price: 1249, inStock: false, rating: 4.7 }
    ]
  },
  {
    id: '2',
    name: 'Samsung Galaxy S24 Ultra',
    price: 1099,
    rating: 4.7,
    reviews: 1923,
    image: 'https://via.placeholder.com/300x300/6366F1/FFFFFF?text=Galaxy+S24',
    brand: 'Samsung',
    category: 'Electronics',
    platform: 'Best Buy',
    inStock: true,
    features: ['S Pen Included', '200MP Camera', '5000mAh Battery'],
    description: 'Samsung Galaxy S24 Ultra with S Pen, advanced AI features, and professional-grade camera.',
    specifications: {
      'Display': '6.8-inch Dynamic AMOLED 2X',
      'Processor': 'Snapdragon 8 Gen 3',
      'Storage': '256GB',
      'Camera': '200MP + 50MP + 12MP + 10MP',
      'Battery': '5000mAh',
      'Weight': '232 grams'
    },
    platforms: [
      { name: 'Samsung', price: 1099, inStock: true, rating: 4.8 },
      { name: 'Best Buy', price: 1099, inStock: true, rating: 4.7 },
      { name: 'Amazon', price: 1149, inStock: true, rating: 4.6 }
    ]
  },
  {
    id: '3',
    name: 'MacBook Air M3 13-inch',
    price: 1099,
    originalPrice: 1199,
    rating: 4.9,
    reviews: 3421,
    image: 'https://via.placeholder.com/300x300/10B981/FFFFFF?text=MacBook+Air',
    brand: 'Apple',
    category: 'Electronics',
    platform: 'Apple Store',
    inStock: false,
    features: ['M3 Chip', '18-hour Battery', 'Liquid Retina Display'],
    description: 'MacBook Air with M3 chip delivers exceptional performance and all-day battery life.',
    specifications: {
      'Display': '13.6-inch Liquid Retina',
      'Chip': 'Apple M3',
      'Memory': '8GB',
      'Storage': '256GB SSD',
      'Battery': 'Up to 18 hours',
      'Weight': '1.24 kg'
    },
    platforms: [
      { name: 'Apple Store', price: 1099, inStock: false, rating: 4.9 },
      { name: 'Amazon', price: 1149, inStock: true, rating: 4.8 },
      { name: 'Best Buy', price: 1099, inStock: true, rating: 4.7 }
    ]
  }
];

  const insertQuery = `
    INSERT INTO products (name, price, original_price, rating, reviews_count, image_url, brand, category, platform, in_stock, features, description, specifications, platforms_data)
    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
    ON CONFLICT (name) DO NOTHING;
  `;

  try {
    for (const product of mockProducts) {
      await db.query(insertQuery, [
        product.name,
        product.price,
        product.originalPrice,
        product.rating,
        product.reviews,
        product.image,
        product.brand,
        product.category,
        product.platform,
        product.inStock,
        JSON.stringify(product.features),
        product.description,
        JSON.stringify(product.specifications),
        JSON.stringify(product.platforms)
      ]);
    }
    console.log('Mock product data inserted successfully.');
  } catch (err) {
    console.error('Error inserting mock data:', err.stack);
  }
};

const initializeDatabase = async () => {
  await createTables();
  await insertMockData();
  console.log('Database initialization complete.');
};

initializeDatabase();