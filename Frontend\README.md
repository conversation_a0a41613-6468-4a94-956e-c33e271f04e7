# Buydex - AI-Powered E-commerce Platform

## Overview
Buydex is a modern, AI-powered e-commerce platform that helps users find perfect products from around the world. The platform features intelligent product recommendations, advanced filtering, and a beautiful, responsive user interface.

## Features

### 🎨 Modern UI Design
- **Futuristic Design**: Clean, minimalist interface with modern aesthetics
- **Color Palette**: Carefully chosen colors (#3B82F6 blue, #6366F1 purple, #10B981 green)
- **Responsive Layout**: Works perfectly on desktop, tablet, and mobile devices
- **Glass Morphism Effects**: Modern visual effects and smooth transitions

### 🔍 Smart Search & Filtering
- **AI-Powered Search**: Intelligent search functionality in the hero section
- **Advanced Filters**: Filter by price range, brand, origin, quality, style, and size
- **Real-time Results**: Instant filtering with live product count updates
- **Reset Functionality**: Easy one-click filter reset

### 🛍️ Product Features
- **Rich Product Cards**: Beautiful product displays with images, ratings, and tags
- **Discount Badges**: Clear discount percentages and savings information
- **Interactive Elements**: Hover effects and smooth animations
- **Detailed Information**: Comprehensive product details including brand, price, and specifications

### 📊 Enhanced User Experience
- **Statistics Section**: Platform statistics (10K+ products, 50+ countries, 98% satisfaction)
- **Hero Section**: Engaging landing area with search functionality
- **Navigation**: Intuitive navigation with fixed header
- **Footer**: Comprehensive footer with company information and links

## Technology Stack

- **Frontend Framework**: React 18 with TypeScript
- **Build Tool**: Vite 7.0.0
- **Styling**: Tailwind CSS 4.1.11
- **Icons**: Lucide React
- **Routing**: React Router DOM 7.6.3
- **Charts**: Recharts 3.0.2

## Getting Started

### Prerequisites
- Node.js (version 16 or higher)
- npm or yarn package manager

### Installation
1. Clone the repository
2. Navigate to the Frontend directory
3. Install dependencies:
   ```bash
   npm install
   ```

### Development
Start the development server:
```bash
npm run dev
```
The application will be available at `http://localhost:3000`

### Build
Create a production build:
```bash
npm run build
```

### Other Scripts
- `npm run lint` - Run ESLint
- `npm run preview` - Preview production build

## Project Structure

```
Frontend/
├── src/
│   ├── components/
│   │   └── Layout/
│   │       └── Navbar.tsx
│   ├── pages/
│   │   ├── Homepage.tsx      # Main landing page
│   │   ├── Search.tsx        # Search results page
│   │   ├── Recommendations.tsx
│   │   ├── ProductDetail.tsx
│   │   └── About.tsx
│   ├── App.tsx
│   └── main.tsx
├── public/
├── package.json
└── vite.config.ts
```

## Key Components

### Homepage.tsx
The main landing page featuring:
- Hero section with search functionality
- Statistics display
- Advanced filtering sidebar
- Product grid with 9 sample products
- Responsive design with mobile optimization

### Features Implemented
1. **Product Filtering**: Multi-criteria filtering system
2. **Search Integration**: Real-time search functionality
3. **Responsive Grid**: Adaptive product layout (1/2/3 columns)
4. **Interactive Elements**: Hover effects, transitions, and animations
5. **Modern Design**: Contemporary UI with gradient backgrounds and shadows

## Sample Data
The platform includes 9 sample products across different categories:
- Electronics (Smart Fitness Watch, Wireless Headphones, Premium Coffee Maker)
- Fashion (Classic Leather Jacket, Designer Sunglasses, Silk Scarf Collection)
- Lifestyle (Organic Skincare Set, Eco-Friendly Water Bottle, Bamboo Phone Case)

## Browser Support
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Contributing
This project follows modern React development practices with TypeScript for type safety and Tailwind CSS for styling.

## License
All rights reserved. Powered by AI.
