<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct HTML Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            color: #e74c3c;
            font-size: 3rem;
            text-align: center;
            margin-bottom: 20px;
        }
        .status {
            background: #2ecc71;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        button {
            background: #3498db;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            font-size: 1.2rem;
            cursor: pointer;
            display: block;
            margin: 20px auto;
        }
        button:hover {
            background: #2980b9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 BUYDEX SERVER TEST</h1>
        <div class="status">
            <h2>✅ Server Status: RUNNING</h2>
            <p>If you can see this page, the web server is working correctly!</p>
        </div>
        
        <h3>Test Results:</h3>
        <ul>
            <li>✅ HTML is rendering</li>
            <li>✅ CSS styles are applied</li>
            <li>✅ Static files are served</li>
            <li>✅ Frontend server is accessible</li>
        </ul>
        
        <button onclick="testJavaScript()">Test JavaScript</button>
        
        <div id="js-result" style="margin-top: 20px;"></div>
        
        <hr style="margin: 30px 0;">
        
        <h3>Next Steps:</h3>
        <p>1. If you can see this page, the server is working</p>
        <p>2. Try accessing the main React app at <a href="/">http://localhost:5173/</a></p>
        <p>3. Check browser console for any JavaScript errors</p>
        
        <div style="background: #f39c12; color: white; padding: 15px; border-radius: 5px; margin-top: 20px;">
            <strong>Current Time:</strong> <span id="current-time"></span>
        </div>
    </div>

    <script>
        function testJavaScript() {
            const result = document.getElementById('js-result');
            result.innerHTML = '<div style="background: #27ae60; color: white; padding: 10px; border-radius: 5px;">✅ JavaScript is working! Time: ' + new Date().toLocaleString() + '</div>';
        }
        
        function updateTime() {
            document.getElementById('current-time').textContent = new Date().toLocaleString();
        }
        
        // Update time every second
        setInterval(updateTime, 1000);
        updateTime();
    </script>
</body>
</html>
