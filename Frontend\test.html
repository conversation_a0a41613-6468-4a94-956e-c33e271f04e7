<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Buydex 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #3B82F6 0%, #6366F1 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        p {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        .status {
            background: rgba(16, 185, 129, 0.2);
            border: 2px solid #10B981;
            padding: 15px 30px;
            border-radius: 10px;
            font-weight: bold;
            font-size: 1.1rem;
        }
        .links {
            margin-top: 30px;
        }
        .links a {
            color: white;
            text-decoration: none;
            background: rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 8px;
            margin: 0 10px;
            display: inline-block;
            transition: all 0.3s ease;
        }
        .links a:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 Buydex 测试页面</h1>
        <p>如果您能看到这个页面，说明服务器正在正常运行！</p>
        <div class="status">
            ✅ 服务器状态：正常运行
        </div>
        <div class="links">
            <a href="/">返回主页</a>
            <a href="/search">搜索页面</a>
            <a href="/recommendations">推荐页面</a>
            <a href="/about">关于页面</a>
        </div>
    </div>
    
    <script>
        console.log('✅ 测试页面加载成功！');
        console.log('🚀 Buydex 服务器正在运行');
        
        // 测试JavaScript功能
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ DOM 加载完成');
            
            // 添加点击事件测试
            const links = document.querySelectorAll('.links a');
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    console.log('🔗 点击链接:', this.href);
                });
            });
        });
    </script>
</body>
</html>
