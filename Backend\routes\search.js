const express = require('express');
const db = require('../config/db');
const router = express.Router();

// POST /api/search - Redirect to the main products filtering endpoint
router.post('/', (req, res) => {
  const { query, filters = {}, page = 1, limit = 12 } = req.body;
  const queryParams = new URLSearchParams();

  if (query) queryParams.set('search', query);
  if (page) queryParams.set('page', page);
  if (limit) queryParams.set('limit', limit);
  
  for (const [key, value] of Object.entries(filters)) {
    if (value) queryParams.set(key, value);
  }

  // Redirect to the GET /api/products endpoint with all the parameters
  res.redirect(307, `/api/products?${queryParams.toString()}`);
});

// GET /api/search/suggestions - Get search suggestions from the database
router.get('/suggestions', async (req, res) => {
  try {
    const { q } = req.query;
    if (!q || q.trim().length === 0) {
      return res.json({ success: true, data: { suggestions: [] } });
    }

    const query = `
      SELECT name FROM products
      WHERE LOWER(name) LIKE $1
      GROUP BY name
      ORDER BY COUNT(*) DESC, name
      LIMIT 8;
    `;
    const result = await db.query(query, [`%${q.toLowerCase()}%`]);
    const suggestions = result.rows.map(r => r.name);

    res.json({
      success: true,
      data: { suggestions, query: q },
    });
  } catch (error) {
    console.error('Failed to fetch suggestions:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// GET /api/search/trending - (Still mock data)
router.get('/trending', (req, res) => {
  const trendingSearches = [
    { query: 'iPhone 15', count: 15420 },
    { query: 'MacBook Pro', count: 12350 },
  ];
  res.json({ success: true, data: { trending: trendingSearches } });
});

module.exports = router;