import React from 'react';
import { Link } from 'react-router-dom';
import { Search, Zap, Shield, TrendingUp, Smartphone, Home, Shirt, ArrowRight } from 'lucide-react';

const Homepage: React.FC = () => {
  const categories = [
    { name: 'Electronics', icon: Smartphone, color: 'from-blue-500 to-purple-600' },
    { name: 'Fashion', icon: Shirt, color: 'from-pink-500 to-rose-600' },
    { name: 'Home & Garden', icon: Home, color: 'from-green-500 to-emerald-600' },
  ];

  const features = [
    {
      icon: Zap,
      title: 'AI-Powered Search',
      description: 'Our advanced AI analyzes millions of products to find exactly what you need.',
    },
    {
      icon: Shield,
      title: 'Quality Guaranteed',
      description: 'We verify product quality and authenticity across all partner platforms.',
    },
    {
      icon: TrendingUp,
      title: 'Best Price Tracking',
      description: 'Real-time price monitoring ensures you always get the best deals.',
    },
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="gradient-bg py-20 px-4">
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-gray-dark mb-6 animate-fade-in">
            Find the Best Deal with{' '}
            <span className="text-blue-600">
              AI
            </span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto animate-fade-in">
            Discover the perfect products from multiple e-commerce platforms with our intelligent 
            shopping assistant. Compare prices, quality, and features in seconds.
          </p>
          
          {/* Hero Search Bar */}
          <div className="max-w-2xl mx-auto mb-12 animate-slide-up">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-6 h-6" />
              <input
                type="text"
                placeholder="What are you looking for today?"
                className="w-full pl-12 pr-6 py-4 text-lg border border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all duration-200 shadow-lg"
              />
              <button className="absolute right-2 top-1/2 transform -translate-y-1/2 btn-primary px-6 py-2">
                Search
              </button>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center animate-slide-up">
            <Link to="/recommendations" className="btn-primary px-8 py-3 text-lg">
              Start Shopping
              <ArrowRight className="ml-2 w-5 h-5" />
            </Link>
            <Link to="/about" className="btn-secondary px-8 py-3 text-lg">
              How It Works
            </Link>
          </div>
        </div>
      </section>

      {/* Featured Categories */}
      <section className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-dark mb-4">Popular Categories</h2>
            <p className="text-xl text-gray-600">Explore our most searched product categories</p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {categories.map((category, index) => {
              const Icon = category.icon;
              return (
                <Link
                  key={index}
                  to={`/recommendations?category=${category.name.toLowerCase()}`}
                  className="group card hover:shadow-soft-lg transition-all duration-300 transform hover:-translate-y-2"
                >
                  <div className={`w-16 h-16 bg-gradient-to-r ${category.color} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <Icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-semibold text-gray-dark mb-3">{category.name}</h3>
                  <p className="text-gray-600 mb-4">
                    Discover the best {category.name.toLowerCase()} products with AI-powered recommendations.
                  </p>
                  <div className="flex items-center text-blue-500 font-medium hover:text-blue-600 transition-colors duration-200">
                    Explore Category
                    <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-200" />
                  </div>
                </Link>
              );
            })}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 bg-gray-light">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-dark mb-4">Why Choose Buydex?</h2>
            <p className="text-xl text-gray-600">Advanced AI technology meets smart shopping</p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div key={index} className="text-center group">
                  <div className="w-20 h-20 bg-white rounded-2xl shadow-soft flex items-center justify-center mx-auto mb-6 group-hover:shadow-soft-lg transition-shadow duration-300">
                    <Icon className="w-10 h-10 text-blue-500" />
                  </div>
                  <h3 className="text-2xl font-semibold text-gray-dark mb-4">{feature.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4" style={{background: 'linear-gradient(135deg, #3b82f6 0%, #6366f1 100%)'}}>
        <div className="max-w-4xl mx-auto text-center text-white">
          <h2 className="text-4xl font-bold mb-6">Ready to Start Smart Shopping?</h2>
          <p className="text-xl mb-8 opacity-90">
            Join thousands of users who save time and money with AI-powered product recommendations.
          </p>
          <Link to="/recommendations" className="bg-white text-blue-600 hover:bg-gray-100 font-semibold px-8 py-4 rounded-lg transition-colors duration-200 inline-flex items-center">
            Get Started Now
            <ArrowRight className="ml-2 w-5 h-5" />
          </Link>
        </div>
      </section>
    </div>
  );
};

export default Homepage;
