import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Star, ChevronDown, Search } from 'lucide-react';

const Homepage: React.FC = () => {
  const [priceRange, setPriceRange] = useState('All Prices');
  const [selectedBrands, setSelectedBrands] = useState<string[]>([]);
  const [selectedOrigin, setSelectedOrigin] = useState('');
  const [selectedQuality, setSelectedQuality] = useState('Select Quality');
  const [selectedStyle, setSelectedStyle] = useState('');
  const [selectedSize, setSelectedSize] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const navigate = useNavigate();

  const brands = ['UrbanStyle', 'TechPro', 'PureNature'];

  const mockProducts = [
    {
      id: 1,
      name: 'Classic Leather Jacket',
      brand: 'UrbanStyle',
      price: 249.99,
      originalPrice: 299.99,
      rating: 4.7,
      tags: ['Italy', 'Premium', 'M']
    },
    {
      id: 2,
      name: 'Smart Fitness Watch',
      brand: 'TechPro',
      price: 129.99,
      originalPrice: 199.99,
      rating: 4.5,
      tags: ['Japan', 'High', 'One Size']
    },
    {
      id: 3,
      name: 'Organic Skincare Set',
      brand: 'PureNature',
      price: 89.99,
      originalPrice: 120.00,
      rating: 4.8,
      tags: ['Switzerland', 'Organic', 'Full Set']
    },
    {
      id: 4,
      name: 'Wireless Headphones',
      brand: 'TechPro',
      price: 179.99,
      originalPrice: 249.99,
      rating: 4.6,
      tags: ['Germany', 'Premium', 'Universal']
    },
    {
      id: 5,
      name: 'Designer Sunglasses',
      brand: 'UrbanStyle',
      price: 159.99,
      originalPrice: 199.99,
      rating: 4.4,
      tags: ['France', 'Luxury', 'One Size']
    },
    {
      id: 6,
      name: 'Eco-Friendly Water Bottle',
      brand: 'PureNature',
      price: 29.99,
      originalPrice: 39.99,
      rating: 4.9,
      tags: ['USA', 'Eco', '500ml']
    },
    {
      id: 7,
      name: 'Premium Coffee Maker',
      brand: 'TechPro',
      price: 299.99,
      originalPrice: 399.99,
      rating: 4.7,
      tags: ['Germany', 'Premium', 'Large']
    },
    {
      id: 8,
      name: 'Silk Scarf Collection',
      brand: 'UrbanStyle',
      price: 89.99,
      originalPrice: 129.99,
      rating: 4.6,
      tags: ['Italy', 'Luxury', 'One Size']
    },
    {
      id: 9,
      name: 'Bamboo Phone Case',
      brand: 'PureNature',
      price: 19.99,
      originalPrice: 29.99,
      rating: 4.8,
      tags: ['China', 'Eco', 'Universal']
    }
  ];

  const toggleBrand = (brand: string) => {
    setSelectedBrands(prev =>
      prev.includes(brand)
        ? prev.filter(b => b !== brand)
        : [...prev, brand]
    );
  };

  // Filter products based on selected criteria
  const filteredProducts = mockProducts.filter(product => {
    // Search query filter
    if (searchQuery && !product.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !product.brand.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }

    // Brand filter
    if (selectedBrands.length > 0 && !selectedBrands.includes(product.brand)) {
      return false;
    }

    // Origin filter
    if (selectedOrigin && !product.tags.some(tag =>
      tag.toLowerCase().includes(selectedOrigin.toLowerCase())
    )) {
      return false;
    }

    // Quality filter
    if (selectedQuality !== 'Select Quality') {
      const hasQuality = product.tags.some(tag =>
        tag.toLowerCase().includes(selectedQuality.toLowerCase())
      );
      if (!hasQuality) return false;
    }

    // Style filter
    if (selectedStyle && !product.name.toLowerCase().includes(selectedStyle.toLowerCase())) {
      return false;
    }

    // Price range filter
    if (priceRange !== 'All Prices') {
      const price = product.price;
      switch (priceRange) {
        case 'Under $50':
          if (price >= 50) return false;
          break;
        case '$50 - $100':
          if (price < 50 || price > 100) return false;
          break;
        case '$100 - $200':
          if (price < 100 || price > 200) return false;
          break;
        case '$200+':
          if (price < 200) return false;
          break;
      }
    }

    return true;
  });

  const resetFilters = () => {
    setPriceRange('All Prices');
    setSelectedBrands([]);
    setSelectedOrigin('');
    setSelectedQuality('Select Quality');
    setSelectedStyle('');
    setSelectedSize('');
    setSearchQuery('');
  };

  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-7xl mx-auto px-4 py-12 text-center">
          <h1 className="text-4xl font-bold mb-4">Find Your Perfect Product with AI</h1>
          <p className="text-blue-100 text-lg mb-8">Smart Shopping Assistant for Global Products</p>

          {/* Quick Search */}
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search for products, brands, or categories..."
                className="w-full px-6 py-4 text-gray-900 rounded-full text-lg focus:outline-none focus:ring-4 focus:ring-blue-300 shadow-lg"
              />
              <button className="absolute right-2 top-2 bg-blue-600 text-white p-2 rounded-full hover:bg-blue-700 transition-colors">
                <Search className="w-6 h-6" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="bg-white py-12 border-b">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-blue-600 mb-2">10K+</div>
              <div className="text-gray-600">Products</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-green-600 mb-2">50+</div>
              <div className="text-gray-600">Countries</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-purple-600 mb-2">98%</div>
              <div className="text-gray-600">Satisfaction</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-orange-600 mb-2">24/7</div>
              <div className="text-gray-600">AI Support</div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Left Sidebar - Filters */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Find Your Perfect Product</h2>

              {/* Price Range */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">Price Range</label>
                <div className="relative">
                  <select
                    value={priceRange}
                    onChange={(e) => setPriceRange(e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-md bg-white appearance-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option>All Prices</option>
                    <option>Under $50</option>
                    <option>$50 - $100</option>
                    <option>$100 - $200</option>
                    <option>$200+</option>
                  </select>
                  <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                </div>
              </div>

              {/* Brands */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-3">Brands</label>
                <div className="space-y-2">
                  {brands.map(brand => (
                    <label key={brand} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedBrands.includes(brand)}
                        onChange={() => toggleBrand(brand)}
                        className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">{brand}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Origin */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">Origin</label>
                <input
                  type="text"
                  value={selectedOrigin}
                  onChange={(e) => setSelectedOrigin(e.target.value)}
                  placeholder="Country name"
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Quality */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">Quality</label>
                <div className="relative">
                  <select
                    value={selectedQuality}
                    onChange={(e) => setSelectedQuality(e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-md bg-white appearance-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option>Select Quality</option>
                    <option>Premium</option>
                    <option>High</option>
                    <option>Standard</option>
                  </select>
                  <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                </div>
              </div>

              {/* Style */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">Style</label>
                <input
                  type="text"
                  value={selectedStyle}
                  onChange={(e) => setSelectedStyle(e.target.value)}
                  placeholder="e.g. Modern, Retro"
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Size */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">Size</label>
                <input
                  type="text"
                  value={selectedSize}
                  onChange={(e) => setSelectedSize(e.target.value)}
                  placeholder="e.g. M, One Size"
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                <button
                  onClick={() => navigate('/search')}
                  className="w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors"
                >
                  Find Products
                </button>
                <button
                  onClick={resetFilters}
                  className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-md font-medium hover:bg-gray-200 transition-colors"
                >
                  Reset Filters
                </button>
              </div>
            </div>
          </div>

          {/* Right Content - Products */}
          <div className="lg:col-span-3">
            <div className="mb-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 mb-2">Recommended Products</h2>
                  <p className="text-gray-600">Based on your preferences and AI analysis</p>
                </div>
                <div className="text-sm text-gray-500">
                  {filteredProducts.length} of {mockProducts.length} products
                </div>
              </div>
            </div>

            {/* Products Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredProducts.length > 0 ? filteredProducts.map(product => (
                <div key={product.id} className="bg-white rounded-lg shadow-sm border overflow-hidden hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                  {/* Product Image */}
                  <div className="aspect-w-4 aspect-h-3 bg-gray-200 relative">
                    <div className="w-full h-48 bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center text-gray-500 text-lg font-medium">
                      📦 {product.name.split(' ')[0]}
                    </div>
                    {/* Discount Badge */}
                    <div className="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                      -{Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%
                    </div>
                  </div>

                  {/* Product Info */}
                  <div className="p-4">
                    <h3 className="font-semibold text-gray-900 mb-1">{product.name}</h3>
                    <p className="text-sm text-gray-600 mb-2">by {product.brand}</p>

                    {/* Price and Rating */}
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg font-bold text-gray-900">${product.price}</span>
                        <span className="text-sm text-gray-500 line-through">${product.originalPrice}</span>
                      </div>
                      <div className="flex items-center">
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                        <span className="text-sm text-gray-600 ml-1">{product.rating}</span>
                      </div>
                    </div>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-2 mb-4">
                      {product.tags.map((tag, index) => {
                        const tagColors = [
                          'bg-blue-100 text-blue-800',
                          'bg-green-100 text-green-800',
                          'bg-purple-100 text-purple-800'
                        ];
                        return (
                          <span key={index} className={`px-2 py-1 text-xs rounded-full font-medium ${tagColors[index % tagColors.length]}`}>
                            {tag}
                          </span>
                        );
                      })}
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-2">
                      <button className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors">
                        Buy Now
                      </button>
                      <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors">
                        Compare
                      </button>
                    </div>
                  </div>
                </div>
              )) : (
                <div className="col-span-full text-center py-12">
                  <div className="text-gray-500 text-lg mb-2">No products found</div>
                  <p className="text-gray-400">Try adjusting your filters to see more results</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 mt-16">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-xl font-bold mb-4">Buydex</h3>
              <p className="text-gray-400">AI-powered shopping assistant for finding the perfect products worldwide.</p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Products</h4>
              <ul className="space-y-2 text-gray-400">
                <li>Electronics</li>
                <li>Fashion</li>
                <li>Home & Garden</li>
                <li>Health & Beauty</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-gray-400">
                <li>Help Center</li>
                <li>Contact Us</li>
                <li>Shipping Info</li>
                <li>Returns</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Company</h4>
              <ul className="space-y-2 text-gray-400">
                <li>About Us</li>
                <li>Careers</li>
                <li>Privacy Policy</li>
                <li>Terms of Service</li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 Buydex. All rights reserved. Powered by AI.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Homepage;