import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Search, Zap, Shield, TrendingUp, Smartphone, Home, Shirt, ArrowRight, Sparkles, Star } from 'lucide-react';

const Homepage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const navigate = useNavigate();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  const categories = [
    { 
      name: 'Electronics', 
      icon: Smartphone, 
      gradient: 'from-blue-500 to-purple-600',
      description: 'Latest tech & gadgets',
      count: '10K+ products'
    },
    { 
      name: 'Fashion', 
      icon: Shirt, 
      gradient: 'from-pink-500 to-rose-600',
      description: 'Trending styles',
      count: '25K+ products'
    },
    { 
      name: 'Home & Garden', 
      icon: Home, 
      gradient: 'from-green-500 to-emerald-600',
      description: 'Smart living solutions',
      count: '15K+ products'
    },
  ];

  const features = [
    {
      icon: Zap,
      title: 'AI-Powered Search',
      description: 'Advanced AI analyzes millions of products to find exactly what you need with natural language understanding.',
      highlight: 'Smart & Fast'
    },
    {
      icon: Shield,
      title: 'Quality Guaranteed',
      description: 'We verify product quality and authenticity across all partner platforms with real user reviews.',
      highlight: 'Trusted & Verified'
    },
    {
      icon: TrendingUp,
      title: 'Best Price Tracking',
      description: 'Real-time price monitoring across multiple platforms ensures you always get the best deals.',
      highlight: 'Save More'
    },
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section - Modern Minimalist */}
      <section className="hero-section">
        <div className="container py-32">
          {/* Brand Logo Area */}
          <div className="text-center mb-16">
            <div className="brand-logo mb-8">
              <h1 className="brand-title">
                <Sparkles className="inline-block w-12 h-12 mr-4 text-blue-500" />
                Buydex
              </h1>
              <div className="brand-subtitle">AI-Powered Shopping Platform</div>
            </div>
            
            {/* Main Tagline */}
            <h2 className="hero-title">
              智能购物，从Buydex开始
            </h2>
            <p className="hero-description">
              AI驱动的智能购物平台，为您提供最优质的产品推荐和价格比较服务
            </p>
          </div>
          
          {/* Central Search Box - Glass Morphism */}
          <div className="search-container">
            <form onSubmit={handleSearch} className="search-form">
              <div className="search-input-wrapper">
                <Search className="search-icon" />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="搜索您想要的产品..."
                  className="search-input"
                />
                <button type="submit" className="search-button">
                  <span>搜索</span>
                  <ArrowRight className="w-5 h-5 ml-2" />
                </button>
              </div>
            </form>
          </div>
        </div>
      </section>

      {/* Category Quick Access */}
      <section className="category-section">
        <div className="container py-20">
          <div className="text-center mb-16">
            <h3 className="section-title">分类快捷入口</h3>
            <p className="section-description">快速浏览热门商品分类</p>
          </div>
          
          <div className="category-grid">
            {categories.map((category, index) => {
              const Icon = category.icon;
              return (
                <Link
                  key={index}
                  to={`/recommendations?category=${category.name.toLowerCase()}`}
                  className="category-card"
                >
                  <div className={`category-icon bg-gradient-to-r ${category.gradient}`}>
                    <Icon className="w-8 h-8 text-white" />
                  </div>
                  <h4 className="category-name">{category.name}</h4>
                  <p className="category-description">{category.description}</p>
                  <div className="category-count">{category.count}</div>
                  <div className="category-arrow">
                    <ArrowRight className="w-5 h-5" />
                  </div>
                </Link>
              );
            })}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="features-section">
        <div className="container py-20">
          <div className="text-center mb-16">
            <h3 className="section-title">为什么选择Buydex</h3>
            <p className="section-description">AI导购流程，让购物更智能</p>
          </div>
          
          <div className="features-grid">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div key={index} className="feature-card">
                  <div className="feature-icon">
                    <Icon className="w-10 h-10 text-blue-500" />
                  </div>
                  <div className="feature-highlight">{feature.highlight}</div>
                  <h4 className="feature-title">{feature.title}</h4>
                  <p className="feature-description">{feature.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="container py-20 text-center">
          <h3 className="cta-title">开始您的智能购物之旅</h3>
          <p className="cta-description">立即体验AI驱动的个性化购物推荐</p>
          <div className="cta-buttons">
            <Link to="/recommendations" className="cta-button primary">
              <Star className="w-5 h-5 mr-2" />
              获取推荐
            </Link>
            <Link to="/about" className="cta-button secondary">
              了解更多
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Homepage;
