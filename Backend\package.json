{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.10.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.0.0", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.1", "morgan": "^1.10.0", "pg": "^8.16.3"}, "devDependencies": {"@types/node": "^24.0.7", "nodemon": "^3.1.10", "typescript": "^5.8.3"}}