const express = require('express');
const db = require('../config/db');
const router = express.Router();

// GET /api/products - Get all products with filtering, sorting, and pagination
router.get('/', async (req, res) => {
  try {
    const {
      category,
      brand,
      minPrice,
      maxPrice,
      minRating,
      sortBy = 'relevance',
      page = 1,
      limit = 12,
      search
    } = req.query;

    let queryParams = [];
    let whereClauses = [];

    let baseQuery = `SELECT * FROM products`;
    let countQuery = `SELECT COUNT(*) FROM products`;

    if (search) {
      queryParams.push(`%${search.toLowerCase()}%`);
      whereClauses.push(`(LOWER(name) LIKE $${queryParams.length} OR LOWER(description) LIKE $${queryParams.length} OR LOWER(brand) LIKE $${queryParams.length})`);
    }
    if (category) {
      queryParams.push(category);
      whereClauses.push(`category = $${queryParams.length}`);
    }
    if (brand) {
      queryParams.push(brand);
      whereClauses.push(`brand = $${queryParams.length}`);
    }
    if (minPrice) {
      queryParams.push(minPrice);
      whereClauses.push(`price >= $${queryParams.length}`);
    }
    if (maxPrice) {
      queryParams.push(maxPrice);
      whereClauses.push(`price <= $${queryParams.length}`);
    }
    if (minRating) {
      queryParams.push(minRating);
      whereClauses.push(`rating >= $${queryParams.length}`);
    }

    if (whereClauses.length > 0) {
      const whereString = whereClauses.join(' AND ');
      baseQuery += ` WHERE ${whereString}`;
      countQuery += ` WHERE ${whereString}`;
    }

    // Sorting
    let orderByClause = 'ORDER BY id'; // Default sort
    switch (sortBy) {
      case 'price-low':
        orderByClause = 'ORDER BY price ASC';
        break;
      case 'price-high':
        orderByClause = 'ORDER BY price DESC';
        break;
      case 'rating':
        orderByClause = 'ORDER BY rating DESC';
        break;
      case 'reviews':
        orderByClause = 'ORDER BY reviews_count DESC';
        break;
    }
    baseQuery += ` ${orderByClause}`;

    // Pagination
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    const offset = (pageNum - 1) * limitNum;
    queryParams.push(limitNum, offset);
    baseQuery += ` LIMIT $${queryParams.length - 1} OFFSET $${queryParams.length}`;

    // Execute queries
    const totalProductsResult = await db.query(countQuery, queryParams.slice(0, -2)); // Exclude limit and offset
    const totalProducts = parseInt(totalProductsResult.rows[0].count, 10);

    const productsResult = await db.query(baseQuery, queryParams);
    const products = productsResult.rows;

    res.json({
      success: true,
      data: {
        products,
        pagination: {
          currentPage: pageNum,
          totalPages: Math.ceil(totalProducts / limitNum),
          totalProducts,
          hasNext: pageNum * limitNum < totalProducts,
          hasPrev: pageNum > 1,
        },
      },
    });

  } catch (error) {
    console.error('Failed to fetch products:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch products',
      message: error.message,
    });
  }
});

// GET /api/products/:id - Get single product by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const productResult = await db.query('SELECT * FROM products WHERE id = $1', [id]);

    if (productResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Product not found',
      });
    }

    const product = productResult.rows[0];

    // In a real app, priceHistory and aiAnalysis might be generated dynamically or come from other tables.
    // For now, we return the data stored in the product's JSONB fields.

    res.json({
      success: true,
      data: product,
    });

  } catch (error) {
    console.error(`Failed to fetch product ${req.params.id}:`, error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch product',
      message: error.message,
    });
  }
});

// GET /api/products/category/:category - Get products by category
router.get('/category/:category', async (req, res) => {
  try {
    const { category } = req.params;
    const productsResult = await db.query('SELECT * FROM products WHERE LOWER(category) = LOWER($1) LIMIT 20', [category]);
    const products = productsResult.rows;

    res.json({
      success: true,
      data: {
        category,
        products,
        count: products.length,
      },
    });

  } catch (error) {
    console.error(`Failed to fetch products for category ${req.params.category}:`, error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch products by category',
      message: error.message,
    });
  }
});

module.exports = router;