const express = require('express');
const router = express.Router();

// Mock product data - in real app this would come from database/external APIs
const mockProducts = [
  {
    id: '1',
    name: 'iPhone 15 Pro Max 256GB',
    price: 1199,
    originalPrice: 1299,
    rating: 4.8,
    reviews: 2847,
    image: 'https://via.placeholder.com/300x300/3B82F6/FFFFFF?text=iPhone+15+Pro',
    brand: 'Apple',
    category: 'Electronics',
    platform: 'Amazon',
    inStock: true,
    features: ['A17 Pro Chip', '48MP Camera', 'Titanium Design'],
    description: 'The iPhone 15 Pro Max features a titanium design, A17 Pro chip, and advanced camera system.',
    specifications: {
      'Display': '6.7-inch Super Retina XDR',
      'Chip': 'A17 Pro',
      'Storage': '256GB',
      'Camera': '48MP + 12MP + 12MP',
      'Battery': 'Up to 29 hours video',
      'Weight': '221 grams'
    },
    platforms: [
      { name: 'Amazon', price: 1199, inStock: true, rating: 4.8 },
      { name: 'Apple Store', price: 1199, inStock: true, rating: 4.9 },
      { name: 'Best Buy', price: 1249, inStock: false, rating: 4.7 }
    ]
  },
  {
    id: '2',
    name: 'Samsung Galaxy S24 Ultra',
    price: 1099,
    rating: 4.7,
    reviews: 1923,
    image: 'https://via.placeholder.com/300x300/6366F1/FFFFFF?text=Galaxy+S24',
    brand: 'Samsung',
    category: 'Electronics',
    platform: 'Best Buy',
    inStock: true,
    features: ['S Pen Included', '200MP Camera', '5000mAh Battery'],
    description: 'Samsung Galaxy S24 Ultra with S Pen, advanced AI features, and professional-grade camera.',
    specifications: {
      'Display': '6.8-inch Dynamic AMOLED 2X',
      'Processor': 'Snapdragon 8 Gen 3',
      'Storage': '256GB',
      'Camera': '200MP + 50MP + 12MP + 10MP',
      'Battery': '5000mAh',
      'Weight': '232 grams'
    },
    platforms: [
      { name: 'Samsung', price: 1099, inStock: true, rating: 4.8 },
      { name: 'Best Buy', price: 1099, inStock: true, rating: 4.7 },
      { name: 'Amazon', price: 1149, inStock: true, rating: 4.6 }
    ]
  },
  {
    id: '3',
    name: 'MacBook Air M3 13-inch',
    price: 1099,
    originalPrice: 1199,
    rating: 4.9,
    reviews: 3421,
    image: 'https://via.placeholder.com/300x300/10B981/FFFFFF?text=MacBook+Air',
    brand: 'Apple',
    category: 'Electronics',
    platform: 'Apple Store',
    inStock: false,
    features: ['M3 Chip', '18-hour Battery', 'Liquid Retina Display'],
    description: 'MacBook Air with M3 chip delivers exceptional performance and all-day battery life.',
    specifications: {
      'Display': '13.6-inch Liquid Retina',
      'Chip': 'Apple M3',
      'Memory': '8GB',
      'Storage': '256GB SSD',
      'Battery': 'Up to 18 hours',
      'Weight': '1.24 kg'
    },
    platforms: [
      { name: 'Apple Store', price: 1099, inStock: false, rating: 4.9 },
      { name: 'Amazon', price: 1149, inStock: true, rating: 4.8 },
      { name: 'Best Buy', price: 1099, inStock: true, rating: 4.7 }
    ]
  }
];

// GET /api/products - Get all products with filtering and pagination
router.get('/', (req, res) => {
  try {
    const {
      category,
      brand,
      minPrice,
      maxPrice,
      minRating,
      sortBy = 'relevance',
      page = 1,
      limit = 12,
      search
    } = req.query;

    let filteredProducts = [...mockProducts];

    // Apply filters
    if (category) {
      filteredProducts = filteredProducts.filter(p => 
        p.category.toLowerCase() === category.toLowerCase()
      );
    }

    if (brand) {
      filteredProducts = filteredProducts.filter(p => 
        p.brand.toLowerCase() === brand.toLowerCase()
      );
    }

    if (minPrice) {
      filteredProducts = filteredProducts.filter(p => p.price >= parseFloat(minPrice));
    }

    if (maxPrice) {
      filteredProducts = filteredProducts.filter(p => p.price <= parseFloat(maxPrice));
    }

    if (minRating) {
      filteredProducts = filteredProducts.filter(p => p.rating >= parseFloat(minRating));
    }

    if (search) {
      const searchTerm = search.toLowerCase();
      filteredProducts = filteredProducts.filter(p => 
        p.name.toLowerCase().includes(searchTerm) ||
        p.brand.toLowerCase().includes(searchTerm) ||
        p.description.toLowerCase().includes(searchTerm)
      );
    }

    // Apply sorting
    switch (sortBy) {
      case 'price-low':
        filteredProducts.sort((a, b) => a.price - b.price);
        break;
      case 'price-high':
        filteredProducts.sort((a, b) => b.price - a.price);
        break;
      case 'rating':
        filteredProducts.sort((a, b) => b.rating - a.rating);
        break;
      case 'reviews':
        filteredProducts.sort((a, b) => b.reviews - a.reviews);
        break;
      default:
        // Keep original order for relevance
        break;
    }

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: {
        products: paginatedProducts,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(filteredProducts.length / limit),
          totalProducts: filteredProducts.length,
          hasNext: endIndex < filteredProducts.length,
          hasPrev: startIndex > 0
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to fetch products',
      message: error.message
    });
  }
});

// GET /api/products/:id - Get single product by ID
router.get('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const product = mockProducts.find(p => p.id === id);

    if (!product) {
      return res.status(404).json({
        success: false,
        error: 'Product not found'
      });
    }

    // Add price history (mock data)
    const priceHistory = [
      { date: '2024-01', price: product.originalPrice || product.price + 100 },
      { date: '2024-02', price: product.originalPrice || product.price + 80 },
      { date: '2024-03', price: product.originalPrice || product.price + 50 },
      { date: '2024-04', price: product.originalPrice || product.price + 30 },
      { date: '2024-05', price: product.price + 20 },
      { date: '2024-06', price: product.price }
    ];

    // Add AI analysis (mock data)
    const aiAnalysis = {
      pros: [
        'Excellent build quality and premium materials',
        'Outstanding performance for the price range',
        'Great camera system with advanced features',
        'Long battery life and fast charging',
        'Regular software updates and support'
      ],
      cons: [
        'Premium pricing compared to alternatives',
        'Limited storage expansion options',
        'Some features may be overkill for casual users',
        'Accessories sold separately'
      ],
      recommendation: `This product is highly recommended for users who prioritize quality and performance. 
        The current price of $${product.price} represents good value considering the features offered.`
    };

    res.json({
      success: true,
      data: {
        ...product,
        priceHistory,
        aiAnalysis
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to fetch product',
      message: error.message
    });
  }
});

// GET /api/products/category/:category - Get products by category
router.get('/category/:category', (req, res) => {
  try {
    const { category } = req.params;
    const products = mockProducts.filter(p => 
      p.category.toLowerCase() === category.toLowerCase()
    );

    res.json({
      success: true,
      data: {
        category,
        products,
        count: products.length
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to fetch products by category',
      message: error.message
    });
  }
});

module.exports = router;
