import React from 'react';

const TestPage: React.FC = () => {
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1 style={{ color: '#3b82f6', fontSize: '2rem', marginBottom: '1rem' }}>
        Buydex Test Page
      </h1>
      <p style={{ fontSize: '1.2rem', color: '#374151', marginBottom: '1rem' }}>
        If you can see this page, the React application is working correctly!
      </p>
      <div style={{ 
        backgroundColor: '#f3f4f6', 
        padding: '1rem', 
        borderRadius: '0.5rem',
        marginBottom: '1rem'
      }}>
        <h2 style={{ color: '#1f2937', fontSize: '1.5rem', marginBottom: '0.5rem' }}>
          System Status
        </h2>
        <ul style={{ color: '#4b5563', lineHeight: '1.6' }}>
          <li>✅ React is rendering</li>
          <li>✅ TypeScript is working</li>
          <li>✅ CSS styles are applied</li>
          <li>✅ Frontend server is running</li>
        </ul>
      </div>
      <button 
        style={{
          backgroundColor: '#3b82f6',
          color: 'white',
          padding: '0.5rem 1rem',
          border: 'none',
          borderRadius: '0.5rem',
          fontSize: '1rem',
          cursor: 'pointer'
        }}
        onClick={() => alert('Button clicked! JavaScript is working.')}
      >
        Test Button
      </button>
    </div>
  );
};

export default TestPage;
