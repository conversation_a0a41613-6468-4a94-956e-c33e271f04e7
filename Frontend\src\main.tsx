import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'

// Simple test to check if <PERSON><PERSON> is working
const TestApp = () => {
  return (
    <div style={{
      padding: '2rem',
      fontFamily: 'Inter, sans-serif',
      background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
      minHeight: '100vh',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <div style={{
        background: 'white',
        padding: '3rem',
        borderRadius: '1.5rem',
        boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
        textAlign: 'center',
        maxWidth: '600px'
      }}>
        <h1 style={{
          fontSize: '3rem',
          fontWeight: '900',
          background: 'linear-gradient(135deg, #3B82F6 0%, #6366F1 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          marginBottom: '1rem'
        }}>
          🚀 Buydex
        </h1>
        <h2 style={{
          fontSize: '1.5rem',
          color: '#1F2937',
          marginBottom: '1rem'
        }}>
          React 应用正常运行！
        </h2>
        <p style={{
          color: '#6B7280',
          fontSize: '1.1rem',
          marginBottom: '2rem'
        }}>
          现代化AI购物平台正在加载中...
        </p>
        <div style={{
          background: 'linear-gradient(135deg, #3B82F6 0%, #6366F1 100%)',
          color: 'white',
          padding: '1rem 2rem',
          borderRadius: '2rem',
          display: 'inline-block',
          fontWeight: '600'
        }}>
          ✅ 系统状态：正常
        </div>
        <div style={{ marginTop: '2rem', fontSize: '0.9rem', color: '#9CA3AF' }}>
          当前时间: {new Date().toLocaleString()}
        </div>
      </div>
    </div>
  );
};

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <TestApp />
  </StrictMode>,
)
