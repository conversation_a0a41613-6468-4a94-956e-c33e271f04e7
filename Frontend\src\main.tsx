import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'

// Simple test component
const SimpleTest = () => {
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1 style={{ color: 'red', fontSize: '3rem' }}>HELLO WORLD!</h1>
      <p style={{ fontSize: '1.5rem', color: 'blue' }}>
        This is a simple test. If you can see this, React is working!
      </p>
      <div style={{ backgroundColor: 'yellow', padding: '10px', margin: '10px 0' }}>
        <strong>Current time: {new Date().toLocaleString()}</strong>
      </div>
    </div>
  );
};

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <SimpleTest />
  </StrictMode>,
)
