@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', system-ui, sans-serif;
  background-color: white;
  color: #1f2937;
  line-height: 1.6;
}

/* Utility classes */
.min-h-screen { min-height: 100vh; }
.max-w-7xl { max-width: 80rem; margin: 0 auto; }
.max-w-6xl { max-width: 72rem; margin: 0 auto; }
.max-w-4xl { max-width: 56rem; margin: 0 auto; }
.max-w-3xl { max-width: 48rem; margin: 0 auto; }
.max-w-2xl { max-width: 42rem; margin: 0 auto; }
.mx-auto { margin-left: auto; margin-right: auto; }
.text-center { text-align: center; }
.text-left { text-align: left; }
.flex { display: flex; }
.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }
.grid { display: grid; }
.relative { position: relative; }
.absolute { position: absolute; }
.sticky { position: sticky; }
.top-0 { top: 0; }
.left-0 { left: 0; }
.right-0 { right: 0; }
.bottom-0 { bottom: 0; }
.z-50 { z-index: 50; }
.w-full { width: 100%; }
.w-8 { width: 2rem; }
.w-6 { width: 1.5rem; }
.w-5 { width: 1.25rem; }
.h-full { height: 100%; }
.h-16 { height: 4rem; }
.h-8 { height: 2rem; }
.h-6 { height: 1.5rem; }
.h-5 { height: 1.25rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.px-8 { padding-left: 2rem; padding-right: 2rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }
.py-12 { padding-top: 3rem; padding-bottom: 3rem; }
.py-16 { padding-top: 4rem; padding-bottom: 4rem; }
.py-20 { padding-top: 5rem; padding-bottom: 5rem; }
.pl-3 { padding-left: 0.75rem; }
.pl-4 { padding-left: 1rem; }
.pl-10 { padding-left: 2.5rem; }
.pr-4 { padding-right: 1rem; }
.pt-8 { padding-top: 2rem; }
.pb-8 { padding-bottom: 2rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-12 { margin-bottom: 3rem; }
.mt-4 { margin-top: 1rem; }
.mt-8 { margin-top: 2rem; }
.ml-2 { margin-left: 0.5rem; }
.mr-2 { margin-right: 0.5rem; }
.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-6 > * + * { margin-left: 1.5rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.flex-1 { flex: 1; }
.flex-col { flex-direction: column; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }

/* Colors */
.bg-white { background-color: white; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-blue-500 { background-color: #3b82f6; }
.bg-blue-600 { background-color: #2563eb; }
.bg-green-500 { background-color: #10b981; }
.bg-green-600 { background-color: #059669; }
.text-white { color: white; }
.text-gray-600 { color: #4b5563; }
.text-gray-700 { color: #374151; }
.text-gray-800 { color: #1f2937; }
.text-gray-900 { color: #111827; }
.text-gray-dark { color: #1f2937; }
.text-blue-500 { color: #3b82f6; }
.text-blue-600 { color: #2563eb; }

/* Typography */
.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }
.text-4xl { font-size: 2.25rem; }
.text-5xl { font-size: 3rem; }
.text-6xl { font-size: 3.75rem; }
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-sans { font-family: 'Inter', system-ui, sans-serif; }

/* Borders */
.border { border-width: 1px; }
.border-gray-100 { border-color: #f3f4f6; }
.border-gray-200 { border-color: #e5e7eb; }
.border-transparent { border-color: transparent; }
.rounded { border-radius: 0.25rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-2xl { border-radius: 1rem; }
.rounded-full { border-radius: 9999px; }

/* Shadows */
.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }
.shadow-soft { box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07); }

/* Transitions */
.transition-colors { transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out; }
.transition-all { transition: all 0.15s ease-in-out; }
.duration-200 { transition-duration: 200ms; }

/* Focus */
.focus\:ring-2:focus { box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1); }
.focus\:ring-blue-500:focus { box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1); }
.focus\:border-transparent:focus { border-color: transparent; }
.outline-none { outline: none; }

/* Hover */
.hover\:bg-gray-50:hover { background-color: #f9fafb; }
.hover\:bg-blue-600:hover { background-color: #2563eb; }
.hover\:bg-green-600:hover { background-color: #059669; }
.hover\:text-blue-500:hover { color: #3b82f6; }

/* Transform */
.transform { transform: translateX(var(--tw-translate-x)) translateY(var(--tw-translate-y)); }
.-translate-y-1\/2 { --tw-translate-y: -50%; transform: translateY(-50%); }

/* Additional utility classes */
.text-gray-400 { color: #9ca3af; }
.bg-white\/95 { background-color: rgba(255, 255, 255, 0.95); }
.backdrop-blur-sm { backdrop-filter: blur(4px); }
.shadow-soft { box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07); }
.shadow-soft-lg { box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1); }
.hover\:shadow-soft-lg:hover { box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1); }
.group:hover .group-hover\:shadow-soft-lg { box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1); }
.group:hover .group-hover\:translate-x-1 { transform: translateX(0.25rem); }
.group:hover .group-hover\:scale-110 { transform: scale(1.1); }
.group:hover .group-hover\:text-blue-600 { color: #2563eb; }
.transition-shadow { transition: box-shadow 0.15s ease-in-out; }
.transition-transform { transition: transform 0.15s ease-in-out; }
.duration-300 { transition-duration: 300ms; }
.leading-relaxed { line-height: 1.625; }
.opacity-90 { opacity: 0.9; }
.inline-flex { display: inline-flex; }
.w-4 { width: 1rem; }
.h-4 { height: 1rem; }
.w-10 { width: 2.5rem; }
.h-10 { height: 2.5rem; }
.w-16 { width: 4rem; }
.h-16 { height: 4rem; }
.w-20 { width: 5rem; }
.h-20 { height: 5rem; }
.hover\:-translate-y-2:hover { transform: translateY(-0.5rem); }
.sm\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }

/* Responsive */
@media (min-width: 640px) {
  .sm\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
}

@media (min-width: 768px) {
  .md\:flex { display: flex; }
  .md\:hidden { display: none; }
  .md\:text-6xl { font-size: 3.75rem; }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
  .lg\:px-8 { padding-left: 2rem; padding-right: 2rem; }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
}

/* Custom components */
.gradient-bg {
  background: linear-gradient(135deg, #eff6ff 0%, #e0e7ff 100%);
}

/* Gradient backgrounds */
.bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }
.from-blue-500 { --tw-gradient-from: #3b82f6; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(59, 130, 246, 0)); }
.to-purple-600 { --tw-gradient-to: #9333ea; }
.from-pink-500 { --tw-gradient-from: #ec4899; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(236, 72, 153, 0)); }
.to-rose-600 { --tw-gradient-to: #e11d48; }
.from-green-500 { --tw-gradient-from: #10b981; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(16, 185, 129, 0)); }
.to-emerald-600 { --tw-gradient-to: #059669; }

/* Simplified gradients for categories */
.from-blue-500.to-purple-600 { background: linear-gradient(135deg, #3b82f6 0%, #9333ea 100%); }
.from-pink-500.to-rose-600 { background: linear-gradient(135deg, #ec4899 0%, #e11d48 100%); }
.from-green-500.to-emerald-600 { background: linear-gradient(135deg, #10b981 0%, #059669 100%); }

.btn-primary {
  background-color: #3b82f6;
  color: white;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
  background-color: #2563eb;
}

.btn-secondary {
  background-color: white;
  color: #1f2937;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  cursor: pointer;
  transition: background-color 0.2s;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
  background-color: #f9fafb;
}

.card {
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  border: 1px solid #f3f4f6;
  padding: 1.5rem;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}
